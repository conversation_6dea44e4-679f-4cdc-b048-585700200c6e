<?php
require_once 'vendor/autoload.php';
require_once 'src/Helpers/response.php';

$config = require_once 'src/config/app.php';

// Récupérer la méthode HTTP et l’URL
$method = $_SERVER['REQUEST_METHOD'];

// Utiliser parse_url pour extraire uniquement le chemin
$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = explode('/', trim($request_uri, '/'));

// Vérifier la version de l’API
if ($uri[0] !== $config['api_version']) {
    sendResponse(404, ['message' => 'Version API invalide']);
    exit;
}

// Router les requêtes vers le bon contrôleur
$resource = $uri[1] ?? '';
$action = $uri[2] ?? '';

switch ($resource) {
    case 'users':
        require_once 'src/Controllers/UserController.php';
        $data = json_decode(file_get_contents('php://input'), true);
        $controller = new UserController();
        if ($method === 'POST' && $action === 'register') {
            $controller->register($data);
        } elseif ($method === 'POST' && $action === 'login') {
            $controller->login($data);
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'routes':
        require_once 'src/Controllers/RouteController.php';
        $data = json_decode(file_get_contents('php://input'), true);
        $controller = new RouteController();
        if ($method === 'GET' && !$action) {
            $controller->getAllRoutes();
        } elseif ($method === 'GET' && $action) {
            $controller->getRouteById($action);
        } elseif ($method === 'POST' && !$action) {
            $controller->createRoute($data);
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'locations':
        require_once 'src/Controllers/LocationController.php';
        $data = json_decode(file_get_contents('php://input'), true);
        $controller = new LocationController();
        if ($method === 'GET' && !$action) {
            $controller->getLocations();
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'trips':
        require_once 'src/Controllers/TripController.php';
        $data = json_decode(file_get_contents('php://input'), true);
        $controller = new TripController();
        if ($method === 'GET' && !$action) {
            $filters = $_GET;
            $controller->searchTrips($filters);
        } elseif ($method === 'GET' && isset($uri[2]) && !isset($uri[3])) {
            $controller->getTripDetails($uri[2]);
        } elseif ($method === 'GET' && isset($uri[3]) && $uri[3] === 'stops') {
            $tripId = $uri[2];  // Récupérer l'ID du trip
            $controller->getTripStops($tripId);
        } elseif ($method === 'POST' && !$action) {
            $controller->createTrip($data);
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'seats':
        require_once 'src/Controllers/TripController.php';
        $controller = new TripController();
        if ($method === 'GET' && !$action) {
            $filters = $_GET;
            $controller->getSeatsAvailability($filters);
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'amenities':
        require_once 'src/Controllers/BusController.php';
        $controller = new BusController();
        if ($method === 'GET' && !$action) {
            $controller->getAllAmenities();
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'bookings':
        require_once 'src/Controllers/BookingController.php';
        $controller = new BookingController();
        if ($method === 'GET' && !$action) {
            $controller->getUserBookings();
        } elseif ($method === 'GET' && $action) {
            $controller->getBookingById($action);
        } elseif ($method === 'POST' && !$action) {
            $controller->createBooking();
        } elseif ($method === 'DELETE' && $action) {
            $controller->cancelBooking($action);
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    case 'payments':
        require_once 'src/Controllers/PaymentController.php';
        $controller = new PaymentController();
        if ($method === 'POST' && !$action) {
            $controller->createPayment();
        } else {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
        }
        break;
    default:
        sendResponse(404, ['message' => 'Ressource non trouvée']);
        break;
}
?>

