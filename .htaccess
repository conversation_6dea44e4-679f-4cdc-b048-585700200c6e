RewriteEngine On
RewriteRule ^(.*)$ index.php [QSA,L]

# CORS - Autoriser les requêtes multi-origines
<IfModule mod_headers.c>
  # Permettre les requêtes venant de toutes les origines (ajustez si nécessaire)
  Header set Access-Control-Allow-Origin "*" 

  # Autoriser les méthodes de requêtes HTTP utilisées (GET, POST, PUT, DELETE, OPTIONS)
  Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"

  # Autoriser les en-têtes spécifiques dans les requêtes
  Header set Access-Control-Allow-Headers "Content-Type, Authorization"

  # Temps de mise en cache des pré-requêtes CORS (1000 secondes)
  Header set Access-Control-Max-Age "1000"
</IfModule>

# Gérer les requêtes OPTIONS (pré-requêtes CORS)
<IfModule mod_rewrite.c>
  RewriteCond %{REQUEST_METHOD} OPTIONS
  RewriteRule ^(.*)$ - [R=200,L]
</IfModule>

# Compression Brotli (si mod_brotli est activé)
<IfModule mod_brotli.c>
    # Activer la compression Brotli pour les types MIME les plus courants
    AddOutputFilterByType BROTLI_COMPRESS text/html text/plain text/xml text/css application/javascript application/json
</IfModule>
