<?php
require_once __DIR__ . '/../Models/BusModel.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class BusController {
    private $busModel;

    public function __construct() {
        $this->busModel = new BusModel();
    }

    // --------------------------
    // Gestion des Bus
    // --------------------------

    /** GET /v1/buses */
    public function getAllBuses(): array {
        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        $buses = $this->busModel->getAllBuses($limit, $offset);
        http_response_code(200);
        return $buses;
    }

    /** POST /v1/buses */
    public function createBus(array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        // Validation
        $required = ['registration_number', 'brand', 'model', 'capacity'];
        foreach ($required as $field) {
            if(empty($request[$field])) {
                http_response_code(400);
                return ['error' => "Champ $field manquant"];
            }
        }

        try {
            $request['created_by'] = $user_id;
            $busId = $this->busModel->createBus($request);
            http_response_code(201);
            return ['bus_id' => $busId];
        } catch (PDOException $e) {
            if($e->errorInfo[1] == 1062) {
                http_response_code(409);
                return ['error' => 'Immatriculation déjà existante'];
            }
            http_response_code(500);
            return ['error' => 'Erreur serveur'];
        }
    }

    /** PUT /v1/buses/{id} */
    public function updateBus(int $busId, array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        if($this->busModel->updateBus($busId, $request)) {
            http_response_code(200);
            return ['message' => 'Bus mis à jour'];
        }
        
        http_response_code(400);
        return ['error' => 'Aucun champ valide'];
    }

    // --------------------------
    // Gestion des Plans de sièges
    // --------------------------

    /** POST /v1/seat-plans */
    public function createSeatPlan(array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        if(empty($request['plan_config'])) {
            http_response_code(400);
            return ['error' => 'Configuration requise'];
        }

        $seatPlanId = $this->busModel->createSeatPlan([
            'plan_config' => $request['plan_config'],
            'layout_details' => $request['layout_details'] ?? null,
            'created_by' => $user_id
        ]);

        http_response_code(201);
        return ['seat_plan_id' => $seatPlanId];
    }

    // --------------------------
    // Gestion des équipements disponibles dans le système
    // --------------------------

    /**
     * Obtenir la liste de tous les commodités disponibles dans le système
     * @return array Tableau de tous les équipements disponibles
     */
    public function getAllAmenities(): array
    {
        $amenities = $this->busModel->getAllAmenities();
        sendResponse(200, $amenities);
    }

    // --------------------------
    // Gestion des Associations Bus-Équipements
    // --------------------------

    /** POST /v1/buses/{id}/amenities */
    public function addAmenityToBus(int $busId, array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        if(empty($request['amenity_id'])) {
            http_response_code(400);
            return ['error' => 'ID équipement manquant'];
        }

        $success = $this->busModel->addBusAmenity(
            $busId, 
            $request['amenity_id'], 
            $user_id
        );

        if(!$success) {
            http_response_code(500);
            return ['error' => 'Échec de l\'association'];
        }

        http_response_code(201);
        return ['message' => 'Équipement ajouté'];
    }
}