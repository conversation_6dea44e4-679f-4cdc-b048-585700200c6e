<?php
require_once __DIR__ . '/../Models/RouteModel.php';
require_once __DIR__ . '/../Models/LocationModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';
require_once __DIR__ . '/../Middlewares/RoleMiddleware.php';

/**
 * Contrôleur pour la gestion des trajets et leurs points d'arrêt
 */
class RouteController {
    private $routeModel;
    private $locationModel;

    public function __construct() {
        $this->routeModel = new RouteModel();
        $this->locationModel = new LocationModel();
    }

    // --------------------------
    // Gestion des Trajets (Routes)
    // --------------------------
    
    /** 
     * Liste tous les trajets (GET /v1/routes)
     * @return array Réponse JSON
     */
    public function getAllRoutes(): array {
        AuthMiddleware::authenticate('operator');
        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        $routes = $this->routeModel->getAllRoutes($limit, $offset);
        
        if(empty($routes)) {
            http_response_code(204);
            return [];
        }

        http_response_code(200);
        return $routes;
    }

    /**
     * Récupère un trajet spécifique (GET /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @return array Réponse JSON
     */
    public function getRouteById(int $routeId): array {
        AuthMiddleware::authenticate('operator');
        $route = $this->routeModel->getRouteById($routeId);
        
        if(!$route) {
            http_response_code(404);
            return ['error' => 'Trajet non trouvé'];
        }

        http_response_code(200);
        return $route;
    }

    /**
     * Crée un nouveau trajet (POST /v1/routes)
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function createRoute(array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        // Définir les règles de validation
        $validationRules = [
            'route_name' => 'required',
            'departure_location_id' => 'required',
            'destination_location_id' => 'required',
        ];
    
        // Validation des données avec validateFields
        validateFields($request, $validationRules);
    
        // Vérification des doublons
        if($this->routeModel->routeNameExists($request['route_name'])) {
            http_response_code(409);
            return ['error' => 'Ce trajet existe déjà'];
        }
    
        // Vérification des lieux
        if(!$this->locationModel->getLocationById($request['departure_location_id'])) {
            http_response_code(404);
            return ['error' => 'Lieu de départ invalide'];
        }

        if(!$this->locationModel->getLocationById($request['destination_location_id'])) {
            http_response_code(404);
            return ['error' => 'Lieu de destination invalide'];
        }

        try {
            $request['created_by'] = $user_id;
            $routeId = $this->routeModel->createRoute($request);
            
            http_response_code(201);
            return [
                'message' => 'Trajet créé avec succès',
                'route_id' => $routeId,
                'link' => "/v1/routes/$routeId"
            ];
        } catch (PDOException $e) {
            http_response_code(500);
            return ['error' => 'Erreur serveur : ' . $e->getMessage()];
        }
    }

    /**
     * Met à jour un trajet (PUT /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @param array $request Données à mettre à jour
     * @return array Réponse JSON
     */
    public function updateRoute(int $routeId, array $request): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->routeModel->updateRoute($routeId, $request);
        
        if(!$success) {
            http_response_code(400);
            return ['error' => 'Aucune donnée valide à mettre à jour'];
        }

        http_response_code(200);
        return ['message' => 'Trajet mis à jour avec succès'];
    }

    /**
     * Supprime un trajet (DELETE /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @return array Réponse JSON
     */
    public function deleteRoute(int $routeId): array {
        AuthMiddleware::authenticate('operator');

        try {
            $success = $this->routeModel->deleteRoute($routeId);
            
            if(!$success) {
                http_response_code(404);
                return ['error' => 'Trajet non trouvé'];
            }

            http_response_code(200);
            return ['message' => 'Trajet supprimé avec succès'];
        } catch (PDOException $e) {
            http_response_code(409);
            return ['error' => 'Impossible de supprimer : des points d\'arrêt sont associés'];
        }
    }

    // --------------------------------
    // Gestion des Points d'arrêt (RouteStops)
    // --------------------------------

    /**
     * Liste les points d'arrêt d'un trajet (GET /v1/routes/{id}/stops)
     * @param int $routeId ID du trajet
     * @return array Réponse JSON
     */
    public function getRouteStops(int $routeId): array {
        $stops = $this->routeModel->getRouteStops($routeId);
        
        if(empty($stops)) {
            http_response_code(204);
            return [];
        }

        http_response_code(200);
        return $stops;
    }

    /**
     * Ajoute un point d'arrêt à un trajet (POST /v1/routes/{id}/stops)
     * @param int $routeId ID du trajet
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function addRouteStop(int $routeId, array $request): array {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        // Validation des données
        $requiredFields = ['stop_id', 'stop_order', 'stop_type'];
        foreach ($requiredFields as $field) {
            if(empty($request[$field])) {
                http_response_code(400);
                return ['error' => "Le champ $field est obligatoire"];
            }
        }

        // Vérification du point d'arrêt
        if(!$this->locationModel->getStopById($request['stop_id'])) {
            http_response_code(404);
            return ['error' => 'Point d\'arrêt invalide'];
        }

        try {
            $request['created_by'] = $user_id;
            $stopId = $this->routeModel->addRouteStop($routeId, $request);
            
            http_response_code(201);
            return [
                'message' => 'Point d\'arrêt ajouté avec succès',
                'route_stop_id' => $stopId
            ];
        } catch (PDOException $e) {
            if($e->errorInfo[1] == 1062) { // Erreur duplicate entry
                http_response_code(409);
                return ['error' => 'Ce point d\'arrêt est déjà associé au trajet'];
            }
            http_response_code(500);
            return ['error' => 'Erreur serveur'];
        }
    }

    /**
     * Supprime un point d'arrêt d'un trajet (DELETE /v1/routes/{id}/stops/{stopId})
     * @param int $routeId ID du trajet
     * @param int $stopId ID du point d'arrêt
     * @return array Réponse JSON
     */
    public function deleteRouteStop(int $routeId, int $stopId): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->routeModel->deleteRouteStop($routeId, $stopId);
        
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Association non trouvée'];
        }

        http_response_code(200);
        return ['message' => 'Point d\'arrêt retiré du trajet'];
    }
}