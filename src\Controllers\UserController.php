<?php
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Helpers/jwt.php';

class UserController {
    private $userModel;

    public function __construct() {
        $this->userModel = new UserModel();
    }

    /**
     * Inscription d'un nouvel utilisateur
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function register(array $request): void {
        // Définir les règles de validation
        $validationRules = [
            'email' => 'required|email',
            'phone' => 'required',
            'password' => 'required',
            'first_name' => 'required',
            'last_name' => 'required'
        ];
    
        // Validation des données avec validateFields
        validateFields($request, $validationRules);
    
        // Vérification des doublons
        if($this->userModel->emailExists($request['email'])) {
            sendResponse(409, ['error' => 'Cet email est déjà utilisé']);
            return;
        }
    
        if($this->userModel->phoneExists($request['phone'])) {
            sendResponse(409, ['error' => 'Ce numéro de téléphone est déjà utilisé']);
            return;
        }
    
        try {
            $user_id = $this->userModel->createUser($request);
            sendResponse(200, ['message' => 'Utilisateur créé avec succès', 'user_id' => $user_id]);
        } catch (PDOException $e) {
            sendResponse(500, ['error' => 'Erreur lors de la création de l\'utilisateur']);
        }
    }

    /**
     * Authentification des utilisateurs
     * @param array $request Tableau contenant les informations d'identification de l'utilisateur.
     * @return void Envoie une réponse JSON contenant le Jeton JWT sous la clé 'token' en cas de succès, ou une erreur sous la clé 'error'.
     * @throws Exception Si les informations d'identification sont invalides
     */
    public function login(array $request): void {
        $validationRules = [
            'identifier' => 'required',
            'password' => 'required',
        ];
    
        validateFields($request, $validationRules);
    
        $user = $this->userModel->findByEmailOrPhone($request['identifier']);
        $roles = $this->userModel->getUserRole($user['user_id']);
        $authorizedRoles = array_column($roles, 'role_type');
        $requestRole = $request['role'] ?? 'traveller';

        if (!in_array($requestRole, $authorizedRoles)) {
            sendResponse(401, ['error' => 'Accès non autorisé ! Vous n\'avez pas les droits suffisants pour accéder à cette ressource.']);
            return;
        }

        if($user && password_verify($request['password'], $user['password_hash'])) {
            $token = generateJWT($user['user_id'], $request['role']);
            sendResponse(200, ['token' => $token]);
            return;
        }
        
        sendResponse(401, ['error' => 'Informations d\'identification non valides']);
    }

    /**
     * Récupère tous les utilisateurs (operator)
     * @return array Liste des utilisateurs
     */
    public function getAllUsers(): array {
        // Vérification des droits
        AuthMiddleware::authenticate('operator');

        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        return $this->userModel->getAllUsers($limit, $offset);
    }

    /**
     * Récupère un utilisateur spécifique
     * @param int $userId ID de l'utilisateur
     * @return array Données utilisateur
     */
    public function getUser(int $userId): array {
        // Vérification des droits
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $userId && $authData['role'] != 'operator') {
            http_response_code(403);
            return ['error' => 'Accès non autorisé'];
        }

        $user = $this->userModel->getUserById($userId);
        if(!$user) {
            http_response_code(404);
            return ['error' => 'Utilisateur non trouvé'];
        }

        return $user;
    }

    /**
     * Met à jour un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param array $request Données à mettre à jour
     * @return array Réponse JSON
     */
    public function updateUser(int $userId, array $request): array {
        // Vérification des droits
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $userId && $authData['role'] != 'operator') {
            http_response_code(403);
            return ['error' => 'Accès non autorisé'];
        }

        $success = $this->userModel->updateUser($userId, $request);
        if(!$success) {
            http_response_code(400);
            return ['error' => 'Aucune donnée valide à mettre à jour'];
        }

        return ['message' => 'Utilisateur mis à jour avec succès'];
    }

    /**
     * Supprime un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return array Réponse JSON
     */
    public function deleteUser(int $userId): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->userModel->deleteUser($userId);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Utilisateur non trouvé'];
        }

        return ['message' => 'Utilisateur supprimé avec succès'];
    }

    /**
     * Ajoute un rôle à un utilisateur (operator)
     * @param int $userId ID de l'utilisateur
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function addUserRole(int $userId, array $request): array {
        AuthMiddleware::authenticate('operator');

        if(empty($request['role_type'])) {
            http_response_code(400);
            return ['error' => 'Le champ role_type est obligatoire'];
        }

        $success = $this->userModel->addUserRole(
            $userId, 
            $request['role_type'], 
            $_SERVER['AUTH_USER_ID'] // ID récupéré du token JWT
        );

        if(!$success) {
            http_response_code(500);
            return ['error' => 'Échec de l\'ajout du rôle'];
        }

        return ['message' => 'Rôle ajouté avec succès'];
    }

    /**
     * Supprime un rôle d'un utilisateur (operator)
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle
     * @return array Réponse JSON
     */
    public function removeUserRole(int $userId, string $roleType): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->userModel->removeUserRole($userId, $roleType);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Rôle non trouvé'];
        }

        return ['message' => 'Rôle supprimé avec succès'];
    }
}
