<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des lieux (locations)
 */
class LocationModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // -------------------------------------------
    // Méthodes pour les Lieux (Routes /locations)
    // -------------------------------------------

    /**
     * Récupère tous les lieux (GET /v1/locations)
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des lieux
     */
    public function getLocations(): ?array {
        $sql = "SELECT location_id, location_name, region, country FROM location WHERE status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * Récupère tous les lieux (GET /v1/locations)
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des lieux
     */
    public function getAllLocations(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM location LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un lieu par ID (GET /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @return array|null Données du lieu ou null
     */
    public function getLocationById(int $locationId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM location WHERE location_id = ?");
        $stmt->execute([$locationId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Crée un nouveau lieu (POST /v1/locations)
     * @param array $data Données du lieu
     * @return int ID du lieu créé
     */
    public function createLocation(array $data): int {
        $sql = "INSERT INTO location (
            location_name, 
            region, 
            country, 
            time_zone, 
            coordinates,
            created_by
        ) VALUES (?, ?, ?, ?, ST_GeomFromText(?), ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['location_name'],
            $data['region'],
            $data['country'],
            $data['time_zone'],
            "POINT({$data['longitude']} {$data['latitude']})",
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un lieu (PUT /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @param array $data Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateLocation(int $locationId, array $data): bool {
        $allowedFields = ['location_name', 'region', 'country', 'time_zone'];
        $setParts = [];
        $params = [':location_id' => $locationId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE location SET ".implode(', ', $setParts)." WHERE location_id = :location_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Supprime un lieu (DELETE /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @return bool Succès de l'opération
     */
    public function deleteLocation(int $locationId): bool {
        $stmt = $this->db->prepare("DELETE FROM location WHERE location_id = ?");
        return $stmt->execute([$locationId]);
    }

    // ------------------------------------------
    // Méthodes pour les Points d'arrêt (Stops)
    // ------------------------------------------

    /**
     * Récupère tous les points d'arrêt (GET /v1/stops)
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des stops
     */
    public function getAllStops(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM stop LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un points d'arrêt par ID (GET /v1/stops/{id})
     * @param int $stopId ID du stop
     * @return array|null Données du point d'arrêt ou null
     */
    public function getStopById(int $stopId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM stop WHERE stop_id = ?");
        $stmt->execute([$stopId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Crée un nouveau point d'arrêt (POST /v1/stops)
     * @param array $data Données du stop
     * @return int ID du stop créé
     */
    public function createStop(array $data): int {
        $sql = "INSERT INTO stop (
            stop_name, 
            address, 
            coordinates,
            location_id,
            created_by
        ) VALUES (?, ?, ST_GeomFromText(?), ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['stop_name'],
            $data['address'],
            "POINT({$data['longitude']} {$data['latitude']})",
            $data['location_id'],
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un point d'arrêt (PUT /v1/stops/{id})
     * @param int $stopId ID du stop
     * @param array $data Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateStop(int $stopId, array $data): bool {
        $allowedFields = ['stop_name', 'address'];
        $setParts = [];
        $params = [':stop_id' => $stopId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE stop SET ".implode(', ', $setParts)." WHERE stop_id = :stop_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Supprime un point d'arrêt (DELETE /v1/stops/{id})
     * @param int $stopId ID du stop
     * @return bool Succès de l'opération
     */
    public function deleteStop(int $stopId): bool {
        $stmt = $this->db->prepare("DELETE FROM stop WHERE stop_id = ?");
        return $stmt->execute([$stopId]);
    }
}