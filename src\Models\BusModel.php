<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des bus, plans de sièges, sièges et équipements
 */
class BusModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // --------------------------
    // Méthodes pour les Bus (Routes /v1/buses)
    // --------------------------

    /** GET /v1/buses */
    public function getAllBuses(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM bus LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** GET /v1/buses/{id} */
    public function getBusById(int $busId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM bus WHERE bus_id = ?");
        $stmt->execute([$busId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /** POST /v1/buses */
    public function createBus(array $data): int {
        $sql = "INSERT INTO bus (
            registration_number,
            brand,
            model,
            capacity,
            bus_type,
            seat_plan_id,
            year_manufactured,
            created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['registration_number'],
            $data['brand'],
            $data['model'],
            $data['capacity'],
            $data['bus_type'] ?? 'standard',
            $data['seat_plan_id'] ?? null,
            $data['year_manufactured'],
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /** PUT /v1/buses/{id} */
    public function updateBus(int $busId, array $data): bool {
        $allowedFields = ['brand', 'model', 'capacity', 'bus_type', 'seat_plan_id', 'status'];
        $setParts = [];
        $params = [':bus_id' => $busId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE bus SET ".implode(', ', $setParts)." WHERE bus_id = :bus_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /** DELETE /v1/buses/{id} */
    public function deleteBus(int $busId): bool {
        $stmt = $this->db->prepare("DELETE FROM bus WHERE bus_id = ?");
        return $stmt->execute([$busId]);
    }

    // ------------------------------
    // Méthodes pour les Plans de sièges (Routes /v1/seat-plans)
    // ------------------------------

    /** GET /v1/seat-plans
     * @param int $limit Limit
     * @param int $offset Offset
     * @return array
    */
    public function getAllSeatPlans(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM seat_plan LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /** GET /v1/seat-plans/{id}
     * @param int $bus_id Bus ID
     * @return ?array or null
     */
    public function getSeatPlan(int $seat_plan_id): ?array {
        $stmt = $this->db->prepare("SELECT * FROM seat_plan WHERE seat_plan_id = :seat_plan_id");
        $stmt->bindValue(':seat_plan_id', $seat_plan_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /** POST /v1/seat-plans */
    public function createSeatPlan(array $data): int {
        $stmt = $this->db->prepare("INSERT INTO seat_plan (plan_config, layout_details, created_by) VALUES (?, ?, ?)");
        $stmt->execute([$data['plan_config'], $data['layout_details'], $data['created_by']]);
        return $this->db->lastInsertId();
    }

    // ... [Méthodes updateSeatPlan, deleteSeatPlan]

    // ------------------------------
    // Méthodes pour les Sièges (Routes /v1/buses/{id}/seats)
    // ------------------------------

    /** GET /v1/buses/{id}/seats */
    public function getBusSeats(int $busId): array {
        $stmt = $this->db->prepare("SELECT seat_id, seat_number, seat_type FROM seat WHERE bus_id = ?");
        $stmt->execute([$busId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/buses/{id}/seats */
    public function addSeat(int $busId, array $data): int {
        $sql = "INSERT INTO seat (bus_id, seat_number, seat_type) VALUES (?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$busId, $data['seat_number'], $data['seat_type'] ?? 'standard']);
        return $this->db->lastInsertId();
    }

    // ... [Méthodes updateSeat, deleteSeat]

    // ------------------------------
    // Méthodes pour les Équipements (Routes /v1/amenities)
    // ------------------------------

    /** GET /v1/amenities */
    public function getAllAmenities(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT amenity_id, amenity_name, description FROM amenity LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/amenities */
    public function createAmenity(array $data): int {
        $stmt = $this->db->prepare("INSERT INTO amenity (amenity_name, description, created_by) VALUES (?, ?, ?)");
        $stmt->execute([$data['amenity_name'], $data['description'], $data['created_by']]);
        return $this->db->lastInsertId();
    }

    // ------------------------------
    // Méthodes pour les Associations Bus-Équipements
    // ------------------------------

    /** GET /v1/buses/{id}/amenities */
    public function getBusAmenities(int $busId): array {
        $stmt = $this->db->prepare("
            SELECT a.amenity_name, a.description FROM bus_amenity ba
            JOIN amenity a ON ba.amenity_id = a.amenity_id
            WHERE ba.bus_id = ?
        ");
        $stmt->execute([$busId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/buses/{id}/amenities */
    public function addBusAmenity(int $busId, int $amenityId, int $adminId): bool {
        $stmt = $this->db->prepare("INSERT INTO bus_amenity (bus_id, amenity_id, created_by) VALUES (?, ?, ?)");
        return $stmt->execute([$busId, $amenityId, $adminId]);
    }

    /** DELETE /v1/buses/{id}/amenities/{amenityId} */
    public function removeBusAmenity(int $busId, int $amenityId): bool {
        $stmt = $this->db->prepare("DELETE FROM bus_amenity WHERE bus_id = ? AND amenity_id = ?");
        return $stmt->execute([$busId, $amenityId]);
    }
}