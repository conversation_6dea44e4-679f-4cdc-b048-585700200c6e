<?php
require_once __DIR__ . '/../Models/PaymentModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class PaymentController {
    private $paymentModel;

    public function __construct() {
        $this->paymentModel = new PaymentModel();
    }

    public function createPayment() {
        $user_id = checkAuth();
        $data = json_decode(file_get_contents('php://input'), true);
        validateFields($data, [
            'booking_id' => 'positiveInt',
            'amount' => 'positiveInt',
            'currency' => 'required',
            'payment_method' => 'required'
        ]);

        $data['created_by'] = $user_id;
        $payment_id = $this->paymentModel->createPayment($data);
        sendResponse(201, ['message' => 'Paiement créé', 'payment_id' => $payment_id]);
    }
}