<?php
require_once __DIR__ . '/../config/app.php';

function getDB() {
    $config = require __DIR__ . '/../config/app.php';
    $dsn = "mysql:host={$config['db']['host']};dbname={$config['db']['name']};charset=utf8";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ];
    try {
        return new PDO($dsn, $config['db']['user'], $config['db']['pass'], $options);
    } catch (PDOException $e) {
        sendResponse(500, ['message' => 'Erreur de connexion : ' . $e->getMessage()]);
        exit;
    }
}