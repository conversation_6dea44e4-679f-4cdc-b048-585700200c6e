<?php
require_once __DIR__ . '/../Helpers/db.php';

class PaymentModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    public function createPayment($data) {
        $sql = "INSERT INTO payment (booking_id, amount, currency, payment_method, created_by) 
                VALUES (:booking_id, :amount, :currency, :payment_method, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($data);
        return $this->db->lastInsertId();
    }
}