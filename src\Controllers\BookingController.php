<?php
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Models/BookedSeatModel.php'; // Nouveau modèle pour les sièges
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class BookingController {
    private $bookingModel;
    private $bookedSeatModel;

    public function __construct() {
        $this->bookingModel = new BookingModel();
        $this->bookedSeatModel = new BookedSeatModel();
    }

    public function getUserBookings() {
        $user_id = checkAuth();
        $bookings = $this->bookingModel->getUserBookings($user_id);
        sendResponse(200, ['bookings' => $bookings]);
    }

    public function getBookingById($id) {
        $user_id = checkAuth();
        $booking = $this->bookingModel->getBookingById($id, $user_id);
        if ($booking) {
            sendResponse(200, ['booking' => $booking]);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée']);
        }
    }

    public function cancelBooking($id) {
        $user_id = checkAuth();
        $affected = $this->bookingModel->cancelBooking($id, $user_id);
        if ($affected) {
            sendResponse(200, ['message' => 'Réservation annulée']);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée ou non autorisée']);
        }
    }

    public function createBooking() {
        $user_id = checkAuth();

        $data = json_decode(file_get_contents('php://input'), true);
        validateFields($data, [
            'trip_id' => 'positiveInt',
            'boarding_stop_id' => 'positiveInt',
            'dropping_stop_id' => 'positiveInt',
            'total_amount' => 'positiveInt',
            'seat_ids' => 'required' // Nouveau : liste des sièges à réserver
        ]);

        // Vérifier que seat_ids est un tableau d’entiers positifs
        if (!is_array($data['seat_ids']) || empty($data['seat_ids'])) {
            sendResponse(400, ['message' => 'seat_ids doit être un tableau non vide d’entiers positifs']);
            return;
        }
        foreach ($data['seat_ids'] as $seat_id) {
            Validator::positiveInt($seat_id, 'seat_ids');
        }

        $data['user_id'] = $user_id;
        $booking_id = $this->bookingModel->createBooking($data);

        // Réserver les sièges
        foreach ($data['seat_ids'] as $seat_id) {
            $this->bookedSeatModel->createBookedSeat([
                'seat_id' => $seat_id,
                'trip_id' => $data['trip_id'],
                'booking_id' => $booking_id,
                'user_id' => $user_id
            ]);
        }

        sendResponse(201, ['message' => 'Réservation créée', 'booking_id' => $booking_id]);
    }
}