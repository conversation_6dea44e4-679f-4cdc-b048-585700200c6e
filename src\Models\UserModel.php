<?php
require_once __DIR__ . '/../Helpers/db.php';

class UserModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    /**
     * Crée un nouvel utilisateur
     * @param array $userData Données utilisateur
     * @return int ID de l'utilisateur créé
     * @throws PDOException
     */
    public function createUser(array $userData): int {
        $sql = "INSERT INTO user (email, phone, password_hash, first_name, last_name) 
                VALUES (:email, :phone, :password_hash, :first_name, :last_name)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':email' => $userData['email'],
            ':phone' => $userData['phone'],
            ':password_hash' => password_hash($userData['password'], PASSWORD_DEFAULT),
            ':first_name' => $userData['first_name'],
            ':last_name' => $userData['last_name']
        ]);

        return $this->db->lastInsertId();
    }

    public function getUserByEmail($email) {
        $sql = "SELECT * FROM user WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':email' => $email]);
        return $stmt->fetch();
    }
    
    /**
     * Trouve un utilisateur par email ou téléphone
     * @param string $identifier Email ou numéro de téléphone
     * @return array|null Données utilisateur ou null
     */
    public function findByEmailOrPhone(string $identifier): ?array {
        $sql = "SELECT * FROM user WHERE email = ? OR phone = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$identifier, $identifier]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Récupère tous les utilisateurs (pour admin)
     * @param int $limit Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des utilisateurs
     */
    public function getAllUsers(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM user LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un utilisateur par ID
     * @param int $userId ID de l'utilisateur
     * @return array|null Données utilisateur ou null
     */
    public function getUserById(int $userId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM user WHERE user_id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Met à jour les informations utilisateur
     * @param int $userId ID de l'utilisateur
     * @param array $updateData Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateUser(int $userId, array $updateData): bool {
        $allowedFields = ['email', 'phone', 'first_name', 'last_name', 'date_of_birth', 'profile_picture'];
        $setParts = [];
        $params = [':user_id' => $userId];

        foreach ($updateData as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE user SET ".implode(', ', $setParts)." WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Supprime un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return bool Succès de l'opération
     */
    public function deleteUser(int $userId): bool {
        $stmt = $this->db->prepare("DELETE FROM user WHERE user_id = ?");
        return $stmt->execute([$userId]);
    }

    // --------------------------
    // Méthodes pour les rôles des utilisateurs
    // --------------------------

    /**
     * Récupérer la liste des rôles d'un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return array Liste des rôles
     */
    public function getUserRole(int $user_id): array {
        $sql = "SELECT role_type FROM user_role 
                WHERE user_id = :user_id AND is_active = TRUE";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Ajoute un rôle à un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle
     * @param int $assignedBy ID de l'admin qui assigne
     * @return bool Succès de l'opération
     */
    public function addUserRole(int $userId, string $roleType, int $assignedBy): bool {
        $sql = "INSERT INTO user_role (user_id, role_type, assigned_by) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE is_active = TRUE";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $roleType, $assignedBy]);
    }

    /**
     * Supprime un rôle d'un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle à supprimer
     * @return bool Succès de l'opération
     */
    public function removeUserRole(int $userId, string $roleType): bool {
        $stmt = $this->db->prepare("DELETE FROM user_role WHERE user_id = ? AND role_type = ?");
        return $stmt->execute([$userId, $roleType]);
    }

    // --------------------------
    // Méthodes de validation
    // --------------------------

    /**
     * Vérifie si l'email existe déjà
     * @param string $email Email à vérifier
     * @return bool Existe ou non
     */
    public function emailExists(string $email): bool {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM user WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Vérifie si le téléphone existe déjà
     * @param string $phone Numéro à vérifier
     * @return bool Existe ou non
     */
    public function phoneExists(string $phone): bool {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM user WHERE phone = ?");
        $stmt->execute([$phone]);
        return $stmt->fetchColumn() > 0;
    }
}