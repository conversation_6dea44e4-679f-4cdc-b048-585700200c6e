<?php
require_once __DIR__ . '/../Helpers/db.php';

class BookedSeatModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    public function createBookedSeat($data) {
        $sql = "INSERT INTO booked_seat (seat_id, trip_id, booking_id, created_by) 
                VALUES (:seat_id, :trip_id, :booking_id, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':seat_id' => $data['seat_id'],
            ':trip_id' => $data['trip_id'],
            ':booking_id' => $data['booking_id'],
            ':created_by' => $data['user_id']
        ]);
    }
}