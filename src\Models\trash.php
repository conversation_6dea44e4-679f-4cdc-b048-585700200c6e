<?php
class TripModel {
    private $db;

    // Méthodes auxiliaires
    private function getTripStops($trip_id) {
        $sql = "SELECT s.stop_name, rs.stop_type, ts.arrival_time
                FROM trip_stop ts
                JOIN stop s ON ts.stop_id = s.stop_id
                JOIN route_stop rs ON s.stop_id = rs.stop_id AND rs.route_id = (SELECT route_id FROM trip WHERE trip_id = :trip_id)
                WHERE ts.trip_id = :trip_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':trip_id' => $trip_id]);
        return $stmt->fetchAll();
    }
    
    private function getBusAmenities($bus_id) {
        $sql = "SELECT a.amenity_name FROM amenity a
                JOIN bus_amenity ba ON a.amenity_id = ba.amenity_id
                WHERE ba.bus_id = :bus_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':bus_id' => $bus_id]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    private function getSeatPlan($bus_id) {
        $sql = "SELECT sp.* FROM seat_plan sp
                JOIN bus b ON sp.seat_plan_id = b.seat_plan_id
                WHERE b.bus_id = :bus_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':bus_id' => $bus_id]);
        return $stmt->fetch();
    }
    
    private function getSeatsWithAvailability($bus_id, $trip_id) {
        // Étape 1 : Récupérer tous les sièges actifs du bus
        $sql_seats = "SELECT s.seat_id, s.seat_number, s.seat_type 
                      FROM seat s 
                      WHERE s.bus_id = :bus_id AND s.status = 'active'";
        $stmt_seats = $this->db->prepare($sql_seats);
        $stmt_seats->execute([':bus_id' => $bus_id]);
        $seats = $stmt_seats->fetchAll();
    
        // Ajouter la clé 'is_available' avec la valeur par défaut true
        foreach ($seats as &$seat) {
            $seat['is_available'] = true;
        }
    
        // Étape 2 : Récupérer les sièges réservés pour ce voyage
        $sql_booked = "SELECT bs.seat_id FROM booked_seat bs 
                       WHERE bs.trip_id = :trip_id";
        $stmt_booked = $this->db->prepare($sql_booked);
        $stmt_booked->execute([':trip_id' => $trip_id]);
        $booked_seats = $stmt_booked->fetchAll(PDO::FETCH_COLUMN);
    
        // Étape 3 : Mettre à jour 'is_available' à false pour les sièges réservés
        foreach ($seats as &$seat) {
            if (in_array($seat['seat_id'], $booked_seats)) {
                $seat['is_available'] = false;
            }
        }
    
        return $seats;
    }
    
    private function getPricing($route_id, $bus_type) {
        // Étape 4 : Récupérer les prix dans la table pricing
        $sql = "SELECT seat_type, price FROM pricing 
                WHERE route_id = :route_id AND bus_type = :bus_type";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':route_id' => $route_id, ':bus_type' => $bus_type]);
        $prices = $stmt->fetchAll();
    
        $pricing = [];
        foreach ($prices as $price) {
            $pricing[$price['seat_type'] . '_seat_price'] = $price['price'];
        }
    
        return $pricing;
    }
    
    private function getAvailableSeatsCount($seats) {
        $available = ['standard' => 0, 'premium' => 0];
        foreach ($seats as $seat) {
            if ($seat['is_available']) {
                $available[$seat['seat_type']]++;
            }
        }
        return $available;
    }
}